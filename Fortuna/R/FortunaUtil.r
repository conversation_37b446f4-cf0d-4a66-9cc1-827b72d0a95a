#' nodoc
#' @export
Assert = RUtil::Assert

#' nodoc
#' @export
GetStrategySince = function(logSource, server, begin) {
    b = begin
    info = logSource$ListLog(server)
    if (!is.null(b))
        info = info[end > b]
    strategy = unique(gsub("\\..*", "", info$symbol))
    return(strategy)
}

#' nodoc
#' @export
GetStrategyAt = function(logSource, server, timePoint) {
    info = logSource$ListLog(server)
    if (!is.null(timePoint))
        info = info[begin <= timePoint & timePoint <= end]
    strategy = unique(info$component)
    return(strategy)
}

#' nodoc
#' @export
GetStrategyName = function(data) {
    return(attr(data, "strategyName"))
}

GetReportStrategyName = function(data) {
    return(attr(data, "reportStrategyName"))
}

#' nodoc
#' @export
GetTotalStrategyName = function() GetConfig(defaultTotalStrategyName, defaultValue = "Total")

#' nodoc
#' @export
GetGrossAnalysisName = function() GetConfig(defaultGrossAnalysisName, defaultValue = "GrossAnalysis")

#' nodoc
#' @export
GetStrategyGroupName = function(data) {
    return(attr(data, "strategyGroup"))
}

IsFakFlag = function(timeInForce) {
    return(timeInForce %in% c("FAK", "FOK", "SFAK"))
}

#' nodoc
#' @export
GetServerGroup = function(data) {
    return(attr(data, "serverGroup"))
}

#' nodoc
#' @export
GetUnionTimestamp = function(list, timeCol = "timestamp") {
    unionTimestamp = NULL
    for (p in list) {
        if (is.null(p)) next
        if (is.null(unionTimestamp)) {
            unionTimestamp = p[[timeCol]]
            tz = attr(unionTimestamp, "tzone")
        } else {
            unionTimestamp = sort(union(unionTimestamp, p[[timeCol]]))
        }
    }
    unionTimestamp = DoubleToTime(unionTimestamp, tz)
}

#' nodoc
#' @export
GetCommonTimestamp = function(list, timeCol = "timestamp") {
    commonTimestamp = NULL
    for (p in list) {
        if (is.null(p)) next
        if (is.null(commonTimestamp)) {
            commonTimestamp = p[[timeCol]]
        } else {
            commonTimestamp = intersect(commonTimestamp, p[[timeCol]])
        }
    }
    commonTimestamp = DoubleToTime(commonTimestamp)
}

#' nodoc
#' @export
AlignToTimeAxis = function(data, timeAxis, timeCol = "timestamp", fill = TRUE) {
    if (IsEmpty(data)) {
        return(NULL)
    }

    alignRecursiveFunc = function(data, timeAxis, timeCol) {
        if (!is.list(data)) {
            return(data)
        }
        if (length(data) == 0) {
            return(data)
        }

        if (timeCol %in% names(data)) {
            hasTimeCol <<- TRUE
            isdt = is.data.table(data)
            if (isdt) data = as.list(data)
            oriAttr = attributes(data)

            if (fill) {
                index = SelectIndex(timeAxis, data[[timeCol]], 1)
            } else {
                index = AF_SelectIndex(timeAxis, data[[timeCol]], 1, fill = fill)
            }

            data = lapply(data, "[", index)
            data[[timeCol]] = timeAxis
            if (isdt) setDT(data)
            RecoverAttr(data, oriAttr)
            return(data)
        } else {
            alignedData = list()

            for (i in seq_along(data)) {
                p = data[[i]]
                if (is.null(p)) next
                alignedData[[i]] = alignRecursiveFunc(p, timeAxis, timeCol = timeCol)
            }
            names(alignedData) = names(data)
            return(alignedData)
        }
    }

    hasTimeCol = FALSE
    alignedData = alignRecursiveFunc(data, timeAxis, timeCol)
    if (!hasTimeCol) {
        WarningLog("AlignToTimeAxis", "input data ", deparse(substitute(data)), " has no time col: ", timeCol)
        return(data)
    }
    return(alignedData)
}

#' nodoc
#' @export
GetValuePerTimePeriod = function(timestamp, value, timePeriod = seconds(60 * 60)) {

    if (IsEmpty(timestamp) || IsEmpty(value)) return(NULL)

    begin = first(timestamp)
    end = last(timestamp)

    mySplitTime = data.table()

    t = begin
    while (t + timePeriod <= end) {
        b = t
        e = t + timePeriod
        mySplitTime = rbind(mySplitTime, data.table(b = b, e = e))
        t = e
    }

    mySplitTime[nrow(mySplitTime), e := end]
    mySplitTime[, bIndex := SelectIndex(b, timestamp, 1)]
    mySplitTime[, eIndex := SelectIndex(e, timestamp, 1)]
    mySplitTime = mySplitTime[-which(eIndex - bIndex <= 1)]
    mySplitTime[, e := timestamp[eIndex]]
    mySplitTime[, b := timestamp[bIndex]]

    bValue = SelectValue(mySplitTime$b, timestamp, value)
    eValue = SelectValue(mySplitTime$e, timestamp, value)
    mySplitTime$value = eValue - bValue

    ret = SelectValue(timestamp, mySplitTime$b, mySplitTime$value)
    return(ret)
}

RenameConflictStrategy = function(allStgData) {
    stgName = names(allStgData)
    t = table(stgName)
    t = t[t > 1]
    for(i in seq_along(t)) {
        idx = which(stgName == names(t)[i])
        names(allStgData)[idx] = paste(names(t)[i], seq_along(idx), sep = "_")
    }
    return(allStgData)
}

#' nodoc
#' @export
AddTraderToLog = function(log, traderActionLog) {
    if (IsEmpty(traderActionLog)) {
        WarningLog("AddTraderToLog", "This log don't have TraderActionLog. Trader column will be set to 'Unknown': {log$Logger[1]}")
        return(log[, Trader := "Unknown"])
    }

    log[, Trader := traderActionLog[log, roll = "nearest", on = "Timestamp", mult = "last"]$Trader]

    return(log)
}

#' Calculate slots per second from performance data time axis
#' @param perf Performance data with timestamp column or a vector of timestamps
#' @return Number of slots per second
#' @export
GetSlotsPerSecond = function(perf) {
    timeDiffTable = table(as.numeric(diff(perf$timestamp)))
    mostFrequentDiff = as.numeric(names(timeDiffTable)[which.max(timeDiffTable)])
    slotsPerSec = round(1 / mostFrequentDiff)
    return(slotsPerSec)
}
