#' nodoc
#' @export
CalcGrossAnalysisData = function(reportArgs, serverGroup, allData) {
    for (stg in names(allData)) {
        perf = allData[[stg]]$performance
        perfSMA = AF_SMA(perf$performance, 600)
        perfSMA[is.na(perfSMA)] = perf$performance[1]

        slotsPerSec = GetSlotsPerSecond(perf)
        rollingWindow = slotsPerSec * 3600
        growth = GetGrowth(perfSMA, rollingWindow)
        drawdown = GetDrawdown(perfSMA, rollingWindow)

        grossAnalysis = data.table(timestamp = perf$timestamp, growth = growth, drawdown = drawdown)
        grossAnalysis = AlignToTimeAxis(grossAnalysis, allData$Total$performance$timestamp, "timestamp")
        allData[[stg]]$grossAnalysis = grossAnalysis
    }
    return(allData)
}


#' nodoc
#' @export
GetGrowth = function(perf, rollingWindow) {
    if (IsEmpty(perf)) return(NULL)
    rollingMin = AF_RollingMin(perf, rollingWindow)
    growth = perf - rollingMin
    return(growth)
}


#' nodoc
#' @export
GetDrawdown = function(perf, rollingWindow) {
    if (IsEmpty(perf)) return(NULL)
    rollingMax = AF_RollingMax(perf, rollingWindow)
    drawdown = perf - rollingMax
    return(drawdown)
}


#' nodoc
#' @export
DrawGAModule = function(allData, strategy, indicatorName, label, canvas = NULL) {
    Assert(indicatorName %in% c("growth", "drawdown"))
    if (is.null(canvas)) canvas = ggplot()

    for (stg in strategy) {
        grossAnalysis = allData[[stg]]$grossAnalysis
        canvas = DrawTimeSeries(grossAnalysis$timestamp, grossAnalysis[[indicatorName]], canvas, stg)
    }
    canvas = canvas + ggtitle(label)

    return(canvas)
}


#' nodoc
#' @export
DrawAllGAModulePlot = function(reportArgs, serverGroup, allData) {
    filterFuncs = reportArgs$GetGAFilter()
    layout = reportArgs$GetGAModuleLayout()
    Assert(length(filterFuncs) == length(layout$heights))

    filteredList = lapply(filterFuncs, function(func) {
        res = func(allData)
        return(res)
    })

    plotList = list()
    validHeights = c()
    validWidths = c()

    for (i in 1:length(filteredList)) {
        canvas = NULL
        filter = filteredList[[i]]
        canvas = DrawGAModule(allData, filter$strategy, filter$indicatorName, filter$label, canvas)
        if (is.null(canvas)) next
        plotList = c(plotList, list(canvas))
        validHeights = c(validHeights, layout$heights[i])
        validWidths = c(validWidths, layout$widths[i])
    }

    name = GetGrossAnalysisName()
    allData[[name]][["analysisPlot"]] = wrap_plots(plotList, ncol = layout$ncol, heights = validHeights, widths = validWidths)
    setattr(allData[[name]], "strategyName", "GA")
    setattr(allData[[name]], "serverName", serverGroup)
    return(allData)
}


#' A function to filter top N strategies by a given function
#' @param allData A list of data, each element is a series data of a strategy, including 'Total'.
#' @param nTop The number of top strategies to be filtered.
#' @param functionName The function name to be used to filter the strategies. It should be one of 'mean', 'sd', 'median',
#'                     'min', 'max', 'sum', 'prod'.
#' @param indicatorName The indicator name to be used to filter the strategies. It should be one of 'growth', 'drawdown'.
#' @export
FilterTopNByFunction = function(allData, nTop, functionName, indicatorName) {
    if (nTop > length(allData) - 1) {
        ret = list(
            label = glue("Top {nTop} {indicatorName} by {functionName}"),
            indicatorName = indicatorName,
            strategy = names(allData)
        )
        return(ret)
    }

    func = get0(functionName)
    res = lapply(names(allData), function(stg) {
        if (stg == "Total") return(NULL)
        d = allData[[stg]]
        data = d$grossAnalysis[[indicatorName]]
        if (!IsEmpty(data)) {
            result = func(data, na.rm = TRUE)
            return(list(strategy = stg, result = result))
        }
        return(NULL)
    })

    res = Filter(Negate(is.null), res)
    res = rbindlist(res)

    topRes = c("Total", res[order(-abs(result))][1:nTop, strategy])

    ret = list(
        label = glue("Top {nTop} {indicatorName} by {functionName}"),
        indicatorName = indicatorName,
        strategy = topRes
    )
    return(ret)
}

FilterTopNByFunctionInLast2Hours = function(allData, nTop, functionName, indicatorName) {
    lastTimestamp = max(unlist(lapply(allData, function(d) {
        return(d$grossAnalysis$timestamp[length(d$performance$timestamp)])
    })))
    tz = attr(allData$Total$grossAnalysis$timestamp, "tzone")
    begin = as.POSIXct(lastTimestamp - 2 * 60 * 60, origin = "1970-01-01", tz = tz)
    end = as.POSIXct(lastTimestamp, origin = "1970-01-01", tz = tz)

    lastTwoHoursData = lapply(allData, function(d) {
        return(list(grossAnalysis = d$grossAnalysis[timestamp >= begin & timestamp <= end]))
    })
    res = FilterTopNByFunction(lastTwoHoursData, nTop, functionName, indicatorName)
    res = modifyList(res, list(label = glue("Top {nTop} {indicatorName} by {functionName} in last 2 hours")))
    return(res)
}



#' Default filter functions for gross analysis
#' @param data A list of data, each element is a series data of a strategy, including 'Total'.
#' @export
GetDefaultGAFilter = function(data) {
    filterFuncs = list(
        FilterGrowthByMaxInLast2Hours = function(data) {
            FilterTopNByFunctionInLast2Hours(data, 5, "max", "growth")
        },
        FilterDrawdownByMinInLast2Hours = function(data) {
            FilterTopNByFunctionInLast2Hours(data, 5, "min", "drawdown")
        },
        FilterGrowthByMax = function(data) {
            FilterTopNByFunction(data, 5, "max", "growth")
        },
        FilterDrawdownByMin = function(data) {
            FilterTopNByFunction(data, 5, "min", "drawdown")
        }
    )
    return(filterFuncs)
}


#' nodoc
#' @export
GetDefaultGAModuleLayout = function(server, strategy) {
    layout = list(
        heights = c(1, 1, 1, 1),
        # widths = c(1, 1),
        ncol = 1
    )
    return(layout)
}