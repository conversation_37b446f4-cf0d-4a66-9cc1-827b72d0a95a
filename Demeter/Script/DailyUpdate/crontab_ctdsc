PATH=/bin:/usr/bin:/usr/local/bin
FSTRemovePeriod=2010-01-01



@reboot sleep 20; redis-server ~/projects/Aegis/RUtil/RedisDBASignal.conf
@reboot sleep 25; source ~/.bash_profile; Rscript ~/projects/Aegis/Iris/StartRedisDBASignalDashboard.r



# China Future & Option
#20 15 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.DispInput.r type=cf

20 15 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.CFE.r
0  16 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Hades/Hades.r task=CFE
0   4 * * 2   source ~/.bash_profile; Rscript ~/projects/Aegis/Hades/Hades.r task=CFE.Weekly
0  18 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/CrawlSettlement.r
10 18 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Hades/Hades.r task=CFE.Crawl



# China Stock & Bond
#0  16 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.DispInput.r type=cs

0  16 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.CSE.CS.r
0  17 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.CSE.Others.r
0  17 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Hades/Hades.r task=CSE
#0   4 * * 2   source ~/.bash_profile; Rscript ~/projects/Aegis/Hades/Hades.r task=CSE.Weekly



# China Stock others
0   8 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.AKProducts.r
40  8 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.CETF.Constituent.r
30 15 * * 1-5 source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.CS.PxAdj.r
0  17 * * *   source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.CS.Index.r
0   4 * * 7   source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.CS.MD.Fundamental.r



# Crypto
# 0   9 * * *   source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/DU.Crypto.r



# Remove old data
0  15 * * 6   source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/RemoveRawData.r needConfirm=FALSE keepNTradingDays=90
0  16 * * 6   source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/RemoveFSTData.r needConfirm=FALSE date=$FSTRemovePeriod
0   4 * * *   source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/OverwriteMarketData.r remoteHost=ctdsb
0   1 * * *   source ~/.bash_profile; Rscript ~/projects/Aegis/Demeter/ArchiveFinanceNews.r
